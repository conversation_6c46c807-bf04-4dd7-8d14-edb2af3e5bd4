# V2SIM CSV导出数据说明

## 文件列表

### 电动汽车数据
- `ev.csv`: 电动汽车时间序列数据（包含SOC、状态、成本等）
- `ev_by_type.csv`: 按车型分类的电动汽车数据

### 充电站数据
- `fcs.csv`: 快充站运行时间序列数据
- `scs.csv`: 慢充站运行时间序列数据

### 电网数据
- `bus.csv`: 电网母线时间序列数据（电压、负荷等）
- `gen.csv`: 发电机运行时间序列数据
- `line.csv`: 输电线路时间序列数据

### 汇总数据
- `data_overview.csv`: 数据文件概览信息

## 数据格式说明

### 标准时间序列格式
所有CSV文件采用统一的时间序列格式：
```
Time | Time_hours | 设备1 | 设备2 | 设备3 | ...
0    | 0.0        | 值1   | 值2   | 值3   | ...
10   | 0.0028     | 值1   | 值2   | 值3   | ...
20   | 0.0056     | 值1   | 值2   | 值3   | ...
```

### 时间格式
- `Time`: 仿真时间（秒）
- `Time_hours`: 仿真时间（小时）

### 列名格式
- **电动汽车**: v0#soc, v0#sta, v1#soc, v1#sta... (车辆编号#属性)
- **充电站**: fcs0#power, fcs0#count, scs0#power... (站点编号#属性)
- **母线**: b1#voltage, b1#load, b2#voltage... (母线编号#属性)
- **线路**: l1#power, l1#current, l2#power... (线路编号#属性)
- **发电机**: g1#power, g1#voltage, g2#power... (发电机编号#属性)

### 数据单位
- **电压**: 标幺值(p.u.)或千伏(kV)
- **功率**: 兆瓦(MW)或千瓦(kW)
- **电流**: 安培(A)或千安(kA)
- **SOC**: 0-1之间的小数
- **时间**: 秒(s)或小时(h)

## 使用建议

1. **Excel打开**: 文件使用UTF-8-BOM编码，Excel可直接打开显示中文
2. **数据分析**: 推荐使用Python pandas或R进行大数据分析
3. **可视化**: 时间序列格式便于绘制趋势图和对比分析
4. **筛选数据**: 可根据列名筛选特定设备或属性的数据

## 示例用法

### Python读取示例
```python
import pandas as pd

# 读取母线数据
bus_data = pd.read_csv('power_grid/bus_timeseries.csv')

# 绘制母线1电压曲线
import matplotlib.pyplot as plt
plt.plot(bus_data['Time_hours'], bus_data['b1#voltage'])
plt.xlabel('时间(小时)')
plt.ylabel('电压(p.u.)')
plt.title('母线1电压时间序列')
plt.show()
```

生成时间：2025-07-26 18:07:05
