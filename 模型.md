# V2SIM行程链设计与耗电模型技术文档

## 1. 车辆配置参数 (Vehicle Configuration Parameters)

### 1.1 车辆类型参数表（基于ev_types.csv）

| 车辆类型 | 车型ID | 电池容量(kWh) | 续航里程(km) | 快充功率(kW) | 慢充功率(kW) | V2G功率(kW) | 备注 |
|----------|--------|---------------|--------------|--------------|--------------|-------------|------|
| **私家车** | 10 | 65 | 380 | 60 | 7 | 15 | 标准私家车 |
| | 11 | 60 | 340 | 100 | 7 | 20 | 高功率私家车 |
| | 12 | 85 | 450 | 100 | 7 | 25 | 大容量私家车 |
| **出租车** | 20 | 70 | 400 | 100 | 7 | 25 | 标准出租车 |
| | 21 | 85 | 420 | 80 | 7 | 20 | 大容量出租车 |


### 1.3 充电策略参数

| 车辆类型 | 快充SOC阈值 | 慢充SOC阈值 | V2G SOC阈值 | 初始SOC分布 | 说明 |
|----------|-------------|-------------|-------------|-------------|------|
| **私家车** | 0.20-0.25 | 0.40-0.60 | 0.65-0.75 | $N(0.6, 0.1^2)$ | 主要使用慢充 |
| **出租车** | 0.20-0.25 | 0.40-0.60 | 0.65-0.75 | $N(0.6, 0.1^2)$ | 主要使用快充 |

## 2. 工作日出行模式 (Weekday Travel Pattern)

### 2.1 私家车 (Private Vehicles)

#### 行程链类型
| 序号 | 行程链类型 | 描述 | 比例(%) |
|------|------------|------|---------|
| C1 | H-W-H | 家-工作-家 | 55.97 |
| C2 | H-W-SR/SE/O-H | 家-工作-购物/娱乐/其他-家 | 20.34 |
| C3 | H-SR/SE/O-H | 家-购物/娱乐/其他-家 | 23.69 |

#### 出发时间分布模型

**单峰正态分布**
$$f(t_s) = \frac{1}{\sigma\sqrt{2\pi}} e^{-\frac{(t_s-\mu)^2}{2\sigma^2}} \quad (1)$$

**时间参数**
| 参数 | 分布类型 | 数学表达式 | 说明 |
|------|----------|------------|------|
| 从家出发时间 | $N(6.92, 1.24^2)$ | $\mu=6.92$, $\sigma=1.24$ | 早晨通勤时间 |
| 从公司出发时间 | $N(17.47, 1.8^2)$ | $\mu=17.47$, $\sigma=1.8$ | 下班时间 |
| 购物/休闲时间 | $N(2, 0.5^2)$ | $\mu=2$, $\sigma=0.5$ | 停留时长（小时） |

### 2.2 出租车 (Taxis)

#### 运营特征
| 特征 | 描述 | 参数值 |
|------|------|--------|
| 运营模式 | 多次载客行程 | 随机起终点 |
| 每日行程数 | 平均载客次数 | 15次/天 |
| 服务时间 | 运营时间段 | 6:00-22:00 |
| 行程间隔 | 载客间隔时间 | 30-90分钟 |
| 目的地分布 | 载客需求分布 | 家30%、工作30%、休闲20%、其他20% |

#### 出发时间分布模型

**双峰正态分布**
$$f(t_s) = \lambda_1 e^{-\frac{(t_s-\alpha_1)^2}{\beta_1^2}} + \lambda_2 e^{-\frac{(t_s-\alpha_2)^2}{\beta_2^2}} \quad (2)$$

**时间参数**
| 参数 | 分布类型 | 数学表达式 | 说明 |
|------|----------|------------|------|
| 出发时间分布 | 双峰正态分布 | $\lambda_1=0.389, \alpha_1=7.046, \beta_1=1.086$ | 早高峰 |
| | | $\lambda_2=0.066, \alpha_2=15.610, \beta_2=9.667$ | 晚高峰 |
| 运营时间段 | 固定区间 | 6:00-22:00 | 服务时间 |
| 载客间隔时间 | 均匀分布 | 30-90分钟 | 行程间隔 |
| 行程持续时间 | 均匀分布 | 15-30分钟 | 单次行程时长 |

## 3. 周末出行模式 (Weekend Travel Pattern)

### 3.1 私家车 (Private Vehicles)

#### 行程链类型
| 序号 | 行程链类型 | 描述 | 比例(%) |
|------|------------|------|---------|
| C4 | H-SR/SE/O-H | 家-购物/娱乐/其他-家（上午） | 35 |
| C5 | H-SR/SE/O-H | 家-购物/娱乐/其他-家（下午） | 35 |
| 无出行 | - | 无出行 | 30 |

#### 出发时间分布

**时间参数**
| 参数 | 分布类型 | 数学表达式 | 说明 |
|------|----------|------------|------|
| 上午从家出发 | $N(8.98, 3.24^2)$ | $\mu=8.98$, $\sigma=3.24$ | 周末上午出行 |
| 下午从家出发 | $N(16.47, 3.41^2)$ | $\mu=16.47$, $\sigma=3.41$ | 周末下午出行 |
| 购物/休闲时间 | $N(6, 3^2)$ | $\mu=6$, $\sigma=3$ | 停留时长（小时） |

### 3.2 出租车 (Taxis)

#### 运营特征
| 特征 | 描述 | 参数值 |
|------|------|--------|
| 运营强度 | 周末运营强度 | 工作日的80% |
| 每日行程数 | 平均载客次数 | 12次/天（约15×0.8） |
| 服务时间 | 运营时间段 | 6:00-23:00（延长1小时） |
| 目的地分布 | 载客需求分布 | 家30%、工作30%、休闲20%、其他20% |

#### 出发时间分布

**时间参数**
| 参数 | 分布类型 | 数学表达式 | 说明 |
|------|----------|------------|------|
| 出发时间分布 | 双峰正态分布 | $\lambda_1=0.389, \alpha_1=7.046, \beta_1=1.086$ | 早高峰 |
| | | $\lambda_2=0.066, \alpha_2=15.610, \beta_2=9.667$ | 晚高峰 |
| 运营时间段 | 固定区间 | 6:00-23:00（周末延长） | 服务时间 |
| 载客间隔时间 | 均匀分布 | 30-90分钟 | 行程间隔 |
| 行程持续时间 | 均匀分布 | 15-30分钟 | 单次行程时长 |

## 4. 耗电模型 (Energy Consumption Model)

### 4.1 基础能耗计算

$$E_{consumed} = d \times c \times k_{scale} \quad (4)$$

其中：
- $E_{consumed}$: 消耗电量 (kWh)
- $d$: 行驶距离 (m)
- $c$: 能耗系数 (kWh/m)
- $k_{scale}$: 距离缩放因子，$k_{scale} = 1.2$

### 4.2 充电功率修正与效率模型

$$P_{charge,final} = \begin{cases}
P_{rated} \times 0.9 & \text{if } SOC \leq 0.8 \\
P_{rated} \times (3.4 - 3 \times SOC) \times 0.9 & \text{if } SOC > 0.8
\end{cases} \quad (5)$$

$$P_{discharge,final} = P_{rated} \times 0.9 \quad (6)$$

其中：
- $P_{actual}$: SOC修正后充电功率 (kW)
- $P_{rated}$: 额定充电功率 (kW)
- $SOC$: 当前荷电状态
- $\eta_c, \eta_d$: 充放电效率
